# 微信小程序配置说明

## 图片显示问题解决方案

### 1. 域名白名单配置

微信小程序要求所有网络图片的域名都必须在小程序后台配置白名单。

#### 配置步骤：
1. 登录微信小程序后台：https://mp.weixin.qq.com
2. 进入 "开发" -> "开发管理" -> "开发设置"
3. 找到 "服务器域名" 部分
4. 在 "downloadFile合法域名" 中添加以下域名：

```
https://shengdaochejian.top
https://ts.shengdaochejian.top
https://www.shengdaochejian.top
https://img.picui.cn
```

#### 注意事项：
- 域名必须使用 HTTPS 协议
- 不能使用 IP 地址
- 不能带端口号
- 域名配置后需要重新发布小程序才能生效

### 2. 图片加载优化

#### 当前已实现的优化：
1. **自动 HTTPS 转换**：HTTP 图片自动转换为 HTTPS
2. **域名检查**：开发时会提醒未配置的域名
3. **错误重试**：提供图片加载失败重试功能
4. **预下载机制**：使用微信小程序的 downloadFile 预下载图片
5. **详细错误提示**：区分不同类型的加载错误

#### 错误类型说明：
- **域名未配置**：图片域名未在小程序后台配置
- **网络错误**：网络连接问题
- **图片不存在**：图片 URL 无效或图片已删除
- **格式不支持**：图片格式不被支持

### 3. 开发调试

#### 开发者工具设置：
1. 在微信开发者工具中，可以临时关闭域名校验：
   - 点击右上角 "详情"
   - 勾选 "不校验合法域名、web-view（业务域名）、TLS 版本以及 HTTPS 证书"

#### 真机调试：
1. 真机上必须配置正确的域名白名单
2. 可以使用测试页面验证图片加载：`/pages/test/image-test`

### 4. 常见问题

#### Q: 图片在开发者工具中正常，但在真机上显示空白？
A: 这通常是域名白名单问题，请确保所有图片域名都已在小程序后台配置。

#### Q: 图片加载很慢？
A: 可以考虑：
1. 使用 CDN 加速
2. 压缩图片大小
3. 使用 WebP 格式
4. 预加载关键图片

#### Q: 图片显示 "域名未配置" 错误？
A: 需要在微信小程序后台的 "downloadFile合法域名" 中添加对应域名。

### 5. 性能优化建议

1. **图片懒加载**：对于长列表，使用懒加载减少初始加载压力
2. **图片缓存**：微信小程序会自动缓存下载的图片
3. **尺寸优化**：根据显示尺寸选择合适的图片大小
4. **格式选择**：优先使用 WebP 格式，降级到 JPEG/PNG

### 6. 监控和日志

组件已内置详细的日志输出，可以在控制台查看：
- 图片路径处理过程
- 加载状态变化
- 错误详情
- 重试过程

在生产环境中，建议收集这些日志用于问题排查。
