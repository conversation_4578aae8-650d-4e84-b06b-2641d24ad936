<template>
	<view class="container">
		<view class="header">
			<text class="title">图片上传测试</text>
		</view>
		
		<view class="upload-section">
			<text class="section-title">上传图片</text>
			<upload-images 
				:url="uploadUrl"
				:count="4"
				:length="10"
				:initialImages="initialImages"
				:readonly="false"
				:fileExtname="['jpg', 'jpeg', 'png']"
				@success="onUploadSuccess"
				@delete="onImageDelete"
			></upload-images>
		</view>
		
		<view class="info-section">
			<text class="info-title">使用说明：</text>
			<text class="info-text">1. 点击"+"按钮选择图片</text>
			<text class="info-text">2. 支持从相册选择或拍照</text>
			<text class="info-text">3. 支持jpg、jpeg、png格式</text>
			<text class="info-text">4. 最多可上传10张图片</text>
		</view>
	</view>
</template>

<script>
import UploadImages from '@/components/upload-images.vue'

export default {
	components: {
		UploadImages
	},
	data() {
		return {
			uploadUrl: 'https://your-api-domain.com/api/upload', // 替换为你的上传接口
			initialImages: []
		}
	},
	methods: {
		onUploadSuccess(e) {
			console.log('上传成功:', e);
			uni.showToast({
				title: '上传成功',
				icon: 'success'
			});
		},
		onImageDelete(e) {
			console.log('删除图片:', e);
			uni.showToast({
				title: '删除成功',
				icon: 'success'
			});
		}
	}
}
</script>

<style lang="scss">
.container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.header {
	text-align: center;
	padding: 40rpx 0;
	
	.title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}
}

.upload-section {
	background-color: #fff;
	border-radius: 16rpx;
	margin-bottom: 30rpx;
	
	.section-title {
		display: block;
		padding: 30rpx 30rpx 20rpx;
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}
}

.info-section {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 30rpx;
	
	.info-title {
		display: block;
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
	}
	
	.info-text {
		display: block;
		font-size: 26rpx;
		color: #666;
		line-height: 1.6;
		margin-bottom: 10rpx;
	}
}
</style> 